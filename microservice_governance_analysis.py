#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import re
from collections import defaultdict, Counter

def analyze_governance_usage(case):
    """详细分析治理能力使用情况"""
    
    # 治理能力关键词映射
    governance_patterns = {
        '限流': ['限流', 'rate limit', 'throttle', '流量控制'],
        '降级': ['降级', 'degrade', 'fallback', '兜底', '切换', '备用'],
        '熔断': ['熔断', 'circuit breaker', '断路器'],
        '超时': ['超时', 'timeout', '调整超时'],
        '重试': ['重试', 'retry', '重新尝试'],
        '负载均衡': ['负载均衡', 'load balance', '流量分配'],
        '故障转移': ['故障转移', 'failover', '切换'],
        '隔离': ['隔离', 'isolation', '资源隔离']
    }
    
    measures = case.get('应急措施', '').lower()
    cause = case.get('事件原因', '').lower()
    
    used_capabilities = []
    for capability, keywords in governance_patterns.items():
        if any(keyword in measures for keyword in keywords):
            used_capabilities.append(capability)
    
    return used_capabilities

def categorize_microservice_problem(case):
    """分类微服务问题类型"""
    
    problem_categories = {
        'RPC调用': ['rpc', 'grpc', 'dubbo', '服务调用', '接口调用'],
        '网关问题': ['gateway', 'kong', '网关', 'api gateway'],
        '服务发现': ['consul', 'eureka', '服务发现', '注册中心'],
        '负载均衡': ['负载均衡', 'load balance', '流量分配'],
        '数据库连接': ['数据库', 'mysql', 'redis', 'mongodb', '连接池'],
        '消息队列': ['mq', 'kafka', 'rabbitmq', '消息队列'],
        '缓存问题': ['cache', 'redis', '缓存'],
        '第三方依赖': ['第三方', '外部服务', '支付', 'cdn'],
        '容器/K8s': ['容器', 'k8s', 'kubernetes', 'pod', 'docker'],
        '配置问题': ['配置', 'config', 'apollo']
    }
    
    text = f"{case.get('事件描述', '')} {case.get('事件原因', '')}".lower()
    
    categories = []
    for category, keywords in problem_categories.items():
        if any(keyword in text for keyword in keywords):
            categories.append(category)
    
    return categories if categories else ['其他']

def main():
    # 读取JSON文件
    with open('test/noc稳定性事件24.07.10-25.07.01.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    cases = data[0]
    
    # 微服务相关关键词
    microservice_keywords = [
        'rpc', 'api', 'svc', 'service', '服务', '接口', '调用', 
        '超时', '异常', '限流', '降级', '熔断', '重试', 
        'kong', 'gateway', '网关', '负载均衡', '服务发现',
        '微服务', 'consul', 'dubbo', 'grpc', 'http'
    ]
    
    microservice_cases = []
    governance_stats = Counter()
    problem_categories = Counter()
    
    # 分析每个案例
    for case in cases:
        text_content = f"{case.get('事件描述', '')} {case.get('事件原因', '')} {case.get('应急措施', '')}"
        text_content = text_content.lower()
        
        # 判断是否为微服务问题
        is_microservice = any(keyword in text_content for keyword in microservice_keywords)
        
        if is_microservice:
            # 分析治理能力使用
            governance_used = analyze_governance_usage(case)
            
            # 分类问题类型
            categories = categorize_microservice_problem(case)
            
            microservice_cases.append({
                'ID': case.get('ID'),
                'type': case.get('事件类型'),
                'description': case.get('事件描述'),
                'cause': case.get('事件原因'),
                'measures': case.get('应急措施'),
                'governance_used': governance_used,
                'categories': categories,
                'has_governance': len(governance_used) > 0
            })
            
            # 统计治理能力使用
            for capability in governance_used:
                governance_stats[capability] += 1
            
            # 统计问题分类
            for category in categories:
                problem_categories[category] += 1
    
    # 生成报告
    total_microservice = len(microservice_cases)
    with_governance = sum(1 for case in microservice_cases if case['has_governance'])
    without_governance = total_microservice - with_governance
    
    report = f"""# 微服务治理能力使用分析报告

## 📊 总体统计

- **微服务相关事件总数**: {total_microservice}
- **使用治理能力**: {with_governance}条 ({with_governance/total_microservice*100:.1f}%)
- **未使用治理能力**: {without_governance}条 ({without_governance/total_microservice*100:.1f}%)

## 🔧 治理能力使用分布

"""
    
    for capability, count in governance_stats.most_common():
        percentage = count/total_microservice*100
        report += f"- **{capability}**: {count}次 ({percentage:.1f}%)\n"
    
    report += f"\n## 📋 问题类型分布\n\n"
    for category, count in problem_categories.most_common():
        percentage = count/total_microservice*100
        report += f"- **{category}**: {count}条 ({percentage:.1f}%)\n"
    
    # 重点案例分析
    report += f"\n## 🎯 重点案例分析\n\n### ✅ 使用了治理能力的案例\n\n"
    
    governance_cases = [case for case in microservice_cases if case['has_governance']]
    for case in governance_cases[:20]:  # 显示前20个
        report += f"**NOC-{case['ID']} ({case['type']})**: {case['description']}\n"
        report += f"- 治理能力: {', '.join(case['governance_used'])}\n"
        report += f"- 应急措施: {case['measures'][:150]}...\n\n"
    
    report += f"\n### ❌ 未使用治理能力的典型案例\n\n"
    
    no_governance_cases = [case for case in microservice_cases if not case['has_governance']]
    for case in no_governance_cases[:15]:  # 显示前15个
        report += f"**NOC-{case['ID']} ({case['type']})**: {case['description']}\n"
        report += f"- 问题类型: {', '.join(case['categories'])}\n"
        report += f"- 应急措施: {case['measures'][:150]}...\n\n"
    
    # 保存报告
    with open('microservice_governance_detailed_analysis.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"详细治理分析完成！")
    print(f"微服务事件: {total_microservice}条")
    print(f"使用治理能力: {with_governance}条 ({with_governance/total_microservice*100:.1f}%)")
    print(f"治理能力使用最多的是: {governance_stats.most_common(1)[0] if governance_stats else '无'}")

if __name__ == "__main__":
    main()
