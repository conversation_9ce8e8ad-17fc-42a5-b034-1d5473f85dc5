package cn.huolala.arch.hermes.mesh.sentinel.interceptor;

import cn.huolala.arch.hermes.mesh.sentinel.utils.HttpServletUtil;
import cn.lalaframework.tools.util.StrUtil;
import io.prometheus.client.Counter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

@Slf4j
@Component
public class SentinelRequestPerfInterceptor implements HandlerInterceptor {

    /**
     * openapi结果打点
     */
    private final static Counter SENTINEL_API_RESULT_COUNTER = Counter.build().name("hermes_admin_sentinel_invoke_result")
            .help("sentinel invoke result info")
            .labelNames("appid", "domain", "referer", "invokeMethod", "result")
            .maxSamples(50000)  // 增加最大样本数限制
            .register();

    @Override
    public boolean preHandle(HttpServletRequest servletRequest, HttpServletResponse servletResponse, Object handler) throws Exception {
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        if (!(handler instanceof HandlerMethod handlerMethod)) {
            return;
        }
        try {
            String appid = StrUtil.blankToDefault(HttpServletUtil.getAppId(request), "");
            String domain = StrUtil.blankToDefault(HttpServletUtil.getRequestDomain(request), "");
            String referer = StrUtil.blankToDefault(request.getHeader("referer"), "");

            // 拼接controller名称，避免使用url基数爆炸
            String controllerName = handlerMethod.getBeanType().getSimpleName();
            String methodName = handlerMethod.getMethod().getName();
            String invokeMethod = controllerName + "#" + methodName;
            String result = String.valueOf(response.getStatus());
            SENTINEL_API_RESULT_COUNTER.labels(appid, domain, referer, invokeMethod, result).inc();
        } catch (Exception e) {
            log.error("SentinelRequestPerfInterceptor afterCompletion error", e);
        }
     }
}


