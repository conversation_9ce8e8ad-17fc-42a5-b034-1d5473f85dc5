#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
from collections import defaultdict

def main():
    # 读取JSON文件
    with open('test/noc稳定性事件24.07.10-25.07.01.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    cases = data[0]
    
    # 按根因类型分组事件ID
    root_cause_groups = defaultdict(list)
    
    for case in cases:
        root_cause = case.get('根因类型', '未知')
        case_id = case.get('ID', '未知')
        
        if root_cause and root_cause != '未知':
            root_cause_groups[root_cause].append(case_id)
    
    # 按数量排序
    sorted_groups = sorted(root_cause_groups.items(), key=lambda x: len(x[1]), reverse=True)
    
    # 生成报告
    total_cases = len(cases)
    report = "## 🔍 根因类型分布（含事件ID）\n\n"
    
    for root_cause, case_ids in sorted_groups:
        count = len(case_ids)
        percentage = count/total_cases*100
        
        # 格式化事件ID列表
        id_list = ', '.join([f"NOC-{id}" for id in sorted(case_ids, reverse=True)])
        
        report += f"### **{root_cause}**: {count}条 ({percentage:.1f}%)\n"
        report += f"事件ID: {id_list}\n\n"
    
    # 保存到文件
    with open('root_cause_analysis_with_ids.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"根因分析完成！共分析了{total_cases}个案例")
    print(f"根因类型数量: {len(root_cause_groups)}")

if __name__ == "__main__":
    main()
