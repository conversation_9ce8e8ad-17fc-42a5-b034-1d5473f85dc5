# 微服务治理能力使用分析更新报告

## 📊 更新后的统计数据

### 总体情况
- **事件总数**: 137条
- **微服务相关事件**: 121条 (88.3%)
- **使用治理能力事件**: 33条 (27.3%)

### 治理能力使用分类
- **原有治理能力识别**: 33条
- **文本明确提及治理预案**: 17条
- **操作记录匹配**: 1条
- **总计使用治理能力**: 35条

## 🔍 新增治理能力识别详情

### 1. 文本明确提及治理预案的案例

**NOC-2187**: 小拉大盘支付下跌
- 治理关键词: 进行.*降级
- 应急措施: 进行紧急降级...

**NOC-2182**: ud计价异常
- 治理关键词: 进行.*降级
- 应急措施: 研发进行降级飞书发送消息功能，服务随后开始恢复正常。...

**NOC-2180**: Cloudflare无法解析源主机名
- 治理关键词: 进行.*降级
- 应急措施: 1、对cloudflare进行降级
2、网络工程师通过调整网络配置的方式，让终端用户访问其他（雅加达以外）PoP节点，故障得以恢复...

**NOC-2158**: 小拉支付渠道异常
- 治理关键词: 执行.*降级.*预案
- 应急措施: 执行降级预案...

**NOC-2157**: 检索引擎服务异常导致地址检索有结果率下降
- 治理关键词: 触发.*超时.*预案
- 应急措施: 1、09:36分，执行预案降级到多图商
2、10:22分，研发申请对map-textsearch-svc服务扩容 14->18...

**NOC-2155**: 多个核心服务出现异常抖动
- 治理关键词: 超时.*处理
- 应急措施: 联系阿里云紧急处理...

**NOC-2148**: ai-push-api服务异常上升
- 治理关键词: 限流.*处理
- 应急措施: 对服务进行扩容解决
调整调用lbs获取多语言接口的限流阈值： 70 qps --> 100 qps...

**NOC-2138**: 货运服务lbs-map-svc异常升高
- 治理关键词: 执行.*降级.*预案
- 应急措施: 执行地图检索降级到多图商预案...

**NOC-2136**: 小拉支付成功数抖动
- 治理关键词: 进行.*降级
- 应急措施: 对小拉连连支付渠道进行降级...

**NOC-2106**: bme-information-fee-svc zone2 rt上涨
- 治理关键词: 调整.*超时
- 应急措施: 1、ai-orderhall-api调整超时时间（从500毫秒到1秒）
2、zone1已增加node节点
3、bme-information-fee-svc的zone1由100台扩容到300台
4、目前在调整 jetcache大小， 中午重新发布zone2环境
5、晚上发布zone1环境和缩容...

**NOC-2093**: IM聊天功能异常
- 治理关键词: 进行.*降级
- 应急措施: 1、降级一键登录规避im聊天功能
2、已同步客服侧，使用户名密码登录或验证码登录
3、客户端尽快会上线新版本覆盖修复该问题...

**NOC-2081**: 司机无法人脸识别登陆
- 治理关键词: 进行.*降级, 降级.*处理
- 应急措施:  问题期间对人脸识别场景进行降级处理，定位到原因后，后台将app包名检验有效期改至2026年9月30号解决。...

**NOC-2074**: 企业认证页面显示系统故障
- 治理关键词: 降级.*处理
- 应急措施: 临时解决方案：48例技术统一处理，其他需要用户重试解决
最终解决方案：译图供应商今晚发布修复一版，同时产品推进百度供应商接入...

**NOC-2067**: 中信银行故障导致托管银行记账失败
- 治理关键词: 执行.*降级.*预案
- 应急措施: 执行 转账中心中信提现渠道异常降级（预案ID:1957）...

**NOC-2063**: UD 司机无法登录-网关签名失败
- 治理关键词: 进行.*降级
- 应急措施: 对网关签名进行降级，受业务请求签名放行通过...

**NOC-2034**: 司机反馈接到订单进行中的订单都看不到显示连接失败
- 治理关键词: 进行.*降级
- 应急措施: 回滚变更后恢复...

**NOC-2031**: 天眼接口调用失败
- 治理关键词: 进行.*降级
- 应急措施: 对电子签合同进行降级，转为纸质合同签署。...

### 2. 操作记录匹配的案例

**NOC-2053**: 风控risk-themis-service-svc多个pod cpu异常
- 发生时间: 2024-08-19 09:19:17
- 恢复时间: 2024-08-19 09:31:49
- 匹配操作(货运): DEGRADE - bfe-dapi-order-svc
  - 操作时间: 2024-08-19 09:31:10
  - 操作数据: {"name":"risk-themis-service-svc@/common_reminder/getCommonReminderAreaResult","status":"1"}...
  - 匹配原因: 直接appid匹配: bfe-dapi-order-svc <-> bfe-dapi-order-svc


## 🚨 重点微服务问题案例更新

### ✅ 使用治理能力的案例 (更新后: 33条)

#### 1. NOC-2187 (隐患)
- **问题描述**: 小拉大盘支付下跌
- **治理能力类型**: 原有治理, 文本治理预案
- **应急措施**: 进行紧急降级...

#### 2. NOC-2182 (风险)
- **问题描述**: ud计价异常
- **治理能力类型**: 原有治理, 文本治理预案
- **应急措施**: 研发进行降级飞书发送消息功能，服务随后开始恢复正常。...

#### 3. NOC-2180 (隐患)
- **问题描述**: Cloudflare无法解析源主机名
- **治理能力类型**: 原有治理, 文本治理预案
- **应急措施**: 1、对cloudflare进行降级
2、网络工程师通过调整网络配置的方式，让终端用户访问其他（雅加达以外）PoP节点，故障得以恢复...

#### 4. NOC-2173 (隐患)
- **问题描述**: 小拉虚拟号跌底
- **治理能力类型**: 原有治理
- **应急措施**: 紧急降级到真实号码...

#### 5. NOC-2158 (隐患)
- **问题描述**: 小拉支付渠道异常
- **治理能力类型**: 原有治理, 文本治理预案
- **应急措施**: 执行降级预案...

#### 6. NOC-2157 (隐患)
- **问题描述**: 检索引擎服务异常导致地址检索有结果率下降
- **治理能力类型**: 原有治理, 文本治理预案
- **应急措施**: 1、09:36分，执行预案降级到多图商
2、10:22分，研发申请对map-textsearch-svc服务扩容 14->18...

#### 7. NOC-2155 (风险)
- **问题描述**: 多个核心服务出现异常抖动
- **治理能力类型**: 文本治理预案
- **应急措施**: 联系阿里云紧急处理...

#### 8. NOC-2153 (隐患)
- **问题描述**: 支付渠道异常
- **治理能力类型**: 原有治理
- **应急措施**: 降级第三方货运通联间连渠道...

#### 9. NOC-2148 (风险)
- **问题描述**: ai-push-api服务异常上升
- **治理能力类型**: 原有治理, 文本治理预案
- **应急措施**: 对服务进行扩容解决
调整调用lbs获取多语言接口的限流阈值： 70 qps --> 100 qps...

#### 10. NOC-2142 (风险)
- **问题描述**: AWS新加坡机房的专线异常
- **治理能力类型**: 原有治理
- **应急措施**: 切换备用线路解决...

#### 11. NOC-2141 (风险)
- **问题描述**: 客服上报新司机合同无法签署，退出协议白屏
- **治理能力类型**: 原有治理
- **应急措施**: 重启中转服务，降级电子合同，并引导走纸质合同...

#### 12. NOC-2138 (风险)
- **问题描述**: 货运服务lbs-map-svc异常升高
- **治理能力类型**: 原有治理, 文本治理预案
- **应急措施**: 执行地图检索降级到多图商预案...

#### 13. NOC-2136 (隐患)
- **问题描述**: 小拉支付成功数抖动
- **治理能力类型**: 原有治理, 文本治理预案
- **应急措施**: 对小拉连连支付渠道进行降级...

#### 14. NOC-2134 (风险)
- **问题描述**: 货运司机准出服务RT上涨
- **治理能力类型**: 原有治理
- **应急措施**: 1、risk-driver-exit-svc zone1扩容2个pod (2-->4)
2、对异常pod 进行重启
3、对【校验司机是否申请退出平台】做了降级，先不判断，恢复后对降级进行了回滚...

#### 15. NOC-2132 (隐患)
- **问题描述**: 骑手反馈工单无法填写取件码
- **治理能力类型**: 原有治理
- **应急措施**: 1.骑手收取件码功能降级，待产品内部确认后执行
2.用户小程序上周已修复并发布新版本，当前灰度放量中，预计今晚全量
...

#### 16. NOC-2131 (风险)
- **问题描述**: 计价算路调用百度大车RT超时
- **治理能力类型**: 原有治理
- **应急措施**: 地图研发通过把百度切换到高德后恢复...

#### 17. NOC-2129 (隐患)
- **问题描述**: 支付成功数下跌
- **治理能力类型**: 原有治理
- **应急措施**: 紧急降级通联支付渠道，渠道恢复后逐步分批次回滚降级策略...

#### 18. NOC-2120 (冒烟)
- **问题描述**: 攻防演练导致司机成功登录数上涨
- **治理能力类型**: 原有治理
- **应急措施**: 风控降级人脸识别策略...

#### 19. NOC-2106 (风险)
- **问题描述**: bme-information-fee-svc zone2 rt上涨
- **治理能力类型**: 原有治理, 文本治理预案
- **应急措施**: 1、ai-orderhall-api调整超时时间（从500毫秒到1秒）
2、zone1已增加node节点
3、bme-information-fee-svc的zone1由100台扩容到300台
4、目前在调整 jetcache大小， 中午重新发布zone2环境
5、晚上发布zone1环境和缩容...

#### 20. NOC-2101 (风险)
- **问题描述**: TOC服务异常上涨RMQ内存水位上涨
- **治理能力类型**: 原有治理
- **应急措施**: 1）通过降级临时任务恢复
2）研发通过临时修改toc问题任务配置临时解决
3）对bme-trade-freight-core-svc进行少量扩容
4）后续计划下周一发布代码避免重复发生
...

