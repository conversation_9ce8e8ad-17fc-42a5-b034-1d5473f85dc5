# 微服务案例验证报告

## 📊 验证结果统计
- **总事件数**: 137
- **微服务相关**: 83条
- **非微服务相关**: 54条

## ❌ 非微服务相关案例 (54条)

### NOC-2193: 运营活动策略配置错误导致司机领取“L4账号等级(1月)”只生效1天
- **原因**: 之前配置奖励“拉拉值”是在月底最后一天结算，而上月试点配置奖励“等级保护”需要在月初第一天结算，运营配置活动规则奖励策略有问题，导致司机6月30号领取任务奖励“L4等级账号（1个月）”只生效一天
- **排除理由**: 未找到微服务相关关键词

### NOC-2192: 用户app下单超时未支付
- **原因**: 初步确认是华为+Android10 版本，和公司版本无关，老版本也有人遇到，卸载重新安装恢复，下一步重点是和华为沟通
- **排除理由**: 虽有微服务关键词但主要是: 华为

### NOC-2191: 阿里云CDN故障
- **原因**: 阿里云CDN边缘节点异常
- **排除理由**: 未找到微服务相关关键词

### NOC-2190: lbs-driving-svc 异常上涨
- **原因**: lbs-driving-svc 异常上涨问题，根因是ch算路服务一个节点172.18.195.219异常（宿主机硬件故障），恢复方式为摘除异常节点，后续会在20点低峰期替换异常节点。

- **排除理由**: 虽有微服务关键词但主要是: 硬件故障

### NOC-2189: 小拉抢单数持续上涨
- **原因**: 运营侧调整动态调价策略操作不当，导致抢单数异常上涨。
- **排除理由**: 未找到微服务相关关键词

### NOC-2188: hestia异常上涨导致营销活动发奖失败
- **原因**: 配置界面和后台逻辑有一个偶现的bug
- **排除理由**: 未找到微服务相关关键词

### NOC-2184: 多例搬家小哥无法自主叫车
- **原因**: 6.17 晚上线需求[【运营-提配对】家居服务下单选车&小哥一键叫车](https://huolala.feishu.cn/wiki/Eo1NwcbBWiDEbVkIieJchODhnxg)，涉及新增...
- **排除理由**: 未找到微服务相关关键词

### NOC-2181: 货运司机收不到e签宝验证码
- **原因**: 第三方e签宝故障
- **排除理由**: 未找到微服务相关关键词

### NOC-2176: 梅林机房宕机
- **原因**: 梅林机房经营问题，情况比较混乱，已无人值守，初步判断为其他客户搬迁时碰到pdu电源导致部分设备重启，IT同事赶到现场时被物业保安阻拦，耽误恢复时间(机房当前晚上已经不允许进入，包括进机房维护也不行，也...
- **排除理由**: 未找到微服务相关关键词

### NOC-2172: 小拉下单数抖动
- **原因**: 经过排查是交易研发在变更apollo配置时引入了中文逗号，变更后第一时间发现异常马上修正，大盘随后恢复正常
- **排除理由**: 未找到微服务相关关键词

### NOC-2171: 货运大盘下单数同比上涨15%左右
- **原因**: 中午为跑腿客户“大有单”外卖高峰期，正常客户行为导致。
- **排除理由**: 未找到微服务相关关键词

### NOC-2169: 司机侧完成一笔订单就会收到“违规收费”警告
- **原因**: 交易侧预付更便宜需求中将到付服务费包进一口价开量之后导致小车辅助车型一口价订单量上涨引起，引发司机命中警告策略上涨，管控侧属于正常业务逻辑。
- **排除理由**: 未找到微服务相关关键词

### NOC-2168: br多名司机处于账号封停状态
- **原因**: 5.6号bme-dcore-global-task服务的守护进程转化为java服务，因为转发存在缺陷和放量观测不到位导致
- **排除理由**: 未找到微服务相关关键词

### NOC-2165: 司机来电反馈更新安卓版本后有声音但没订单
- **原因**: 2025-03-10交易引擎工程，上线订单推送文案展示优化功能（2025-04-21全量），期间代码引入bug，导致新订单推送到客户端后，小概率发生列表中订单信息的写入时间慢于订单播报时间；研发回滚配...
- **排除理由**: 未找到微服务相关关键词

### NOC-2163: 抢单大厅抖动
- **原因**: 计价组内评估rpric服务存在容量风险进行扩容操作，扩容期间Pod过热引发NODE节点驱逐，导致上游抖动
- **排除理由**: 未找到微服务相关关键词

### NOC-2161: 大盘目的地检索指标跌底
- **原因**: 小拉环境白帽扫描引起xl-lbs-map-svc服务被解除服务注册(注册中心 Consul无可用节点)，导致地图后端检索&逆地理编码接口不可用
PS: 执行map-api.xiaolachuxing....
- **排除理由**: 虽有微服务关键词但主要是: android

### NOC-2159: UD大盘下跌
- **原因**: node节点异常导致，目前定位是环境内存使用率过高导致节点故障

[故障分析](https://huolala.feishu.cn/docx/AzmVd0OGsoRQmGxRx4Ic7nWfnbhUD...
- **排除理由**: 未找到微服务相关关键词

### NOC-2156: 阿里云CDN客户端多例5xx
- **原因**: OSS的某回源IP地址运营商链路访问异常，导致CDN L2节点回源访问异常
- **排除理由**: 未找到微服务相关关键词

### NOC-2155: 多个核心服务出现异常抖动
- **原因**: 12:00，12:06分的两次异常抖动是由于阿里云ecs系统性能下降和阿里云宿主机内存故障，触发两次机器热迁移动作，导致部分集群（ai-orderhall-api、ai-geo-svc、ai-gala...
- **排除理由**: 虽有微服务关键词但主要是: 内存故障

### NOC-2154: 腾讯云sso与api出现问题
- **原因**: 腾讯云广州地域的控制面网络故障。 [深圳货拉拉科技有限公司-腾讯云故障报告-0402](https://huolala.feishu.cn/file/LKbZbZXPIoypiFxepSqcHCKKn...
- **排除理由**: 未找到微服务相关关键词

### NOC-2151: 小拉大盘接单数 四轮小件抖动
- **原因**: 每月一号中午12点会自动触发司机勋章颁发定时任务，三台机器中一台已达到CPU处理上限，另一台也处于高负载运行状态。影响到四轮小件流程节点流转的消息消费。

履约状态同步货运，目前流程是：接单后，小拉侧...
- **排除理由**: 未找到微服务相关关键词

### NOC-2150: 货运司机完单数下跌
- **原因**: 故障主要是由容器SVC组件升级变更操作导致，这次变更操作的主要背景为：
CI团队目前正在做「容器多集群」方案的迁移部署，容器多集群主要是提供容器在多AZ机房的容错能力（用于防范类似于2024.7年阿里...
- **排除理由**: 虽有微服务关键词但主要是: 光缆

### NOC-2147: 推送触达抢单大厅次数抖动
- **原因**: 昨晚断网演练后，消息推送服务zone2的redis节点rt升高，因redis客户端拓扑图未及时更新导致。该问题可通过升级jaf或接入mesh解决，目前仍有约80+服务未完成升级
- **排除理由**: 虽有微服务关键词但主要是: 客户端

### NOC-2146: 小拉部分司机无法绑定银行卡提现
- **原因**: 司机注册时命中中信洗钱高风险/电诈/支付清算黑名单等，导致用户绑卡失败。
- **排除理由**: 未找到微服务相关关键词

### NOC-2144: 货运核心服务抖动
- **原因**: 初步确认是因容器zone2有一台底层宿主机异常引起。
- **排除理由**: 未找到微服务相关关键词

### NOC-2142: AWS新加坡机房的专线异常
- **原因**: 梅林夏龙->AWS新加坡机房的专线（信息平台部网路组同学负责，但专线是三方采购的）出现问题。

- **排除理由**: 未找到微服务相关关键词

### NOC-2140: 核价错误数上涨
- **原因**: iOS 3-11日晚的热修，由于lua代码造成存储数值转换问题，导致下单coupon_id入参异常，从而引起核价异常下单失败。

根因：iOS昨晚有需求发了热修，热修代码是lua语言，lua默认使用d...
- **排除理由**: 未找到微服务相关关键词

### NOC-2128: rh司机无法收到订单推送
- **原因**: ops配置问题，背景为： 1. Bandung近期在招RH司机。申请RH的司机本来物理车型是(6501:只能接货运订单)，运营需要将其改成6502(既能接货运又能接RH单的车型)；2. 但是6502这...
- **排除理由**: 未找到微服务相关关键词

### NOC-2126: cos资料审核无法通过
- **原因**: 经过风控确认，之前图片上传桩点与司机准入桩点图片地址不一致，司机准入的策略判断没有流量进入（拦截未生效）昨天修复了这个图片路径不一致问题后，流量进入了准入桩点策略拦截生效，拦截原因是司机上传图片过程存...
- **排除理由**: 未找到微服务相关关键词

### NOC-2118: 外呼IVR身份识别接口QPS掉底
- **原因**: 兆维联通机房故障引起
- **排除理由**: 未找到微服务相关关键词

### NOC-2116: bfe-uapi-api异常升高
- **原因**: 初步定位是ios token过期的问题，对业务无影响，不影响稳定性
- **排除理由**: 虽有微服务关键词但主要是: ios

### NOC-2114: oimg-oss 多例5xx异常
- **原因**: 阿里云发布了oss图片新组件，由于测试场景不够充分，生产上遇到了特殊的图片格式，引起了服务进程异常，返回5xx异常告警
- **排除理由**: 未找到微服务相关关键词

### NOC-2113: 多个阿里云rds闪断
- **原因**: 部分云服务使用 ECS 服务器中的安全组件的安全检查进程升级时，超预期占用了服务器的内存，导致服务器内存不足，影响了部分云服务。
- **排除理由**: 未找到微服务相关关键词

### NOC-2107: 容器资源内存满导致多个服务抖动
- **原因**: 海外Node受限于购买的机型规格，可能会存在node节点内存满而导致的个服务的pod驱逐现象。
- **排除理由**: 未找到微服务相关关键词

### NOC-2104: 长里程订单减免抽佣异常
- **原因**: 运营新配置90个长里程活动后，由于配置逻辑是不限制城市、全量车型，用户下单后一个订单最多能命中140+活动，出现大对象导致服务GC；进而出现了部分请求超时的现象，活动超时默认的策略是不减免，导致部分订...
- **排除理由**: 虽有微服务关键词但主要是: 运营策略

### NOC-2103: 1122离线数仓埋点数据量缺失30%
- **原因**: 离线数据导入总线任务异常中断
详细的复盘文档：[2024.11.23 [P2][故障]货运埋点总线下跌约30%](https://huolala.feishu.cn/wiki/TkGhwmYwqiRr...
- **排除理由**: 未找到微服务相关关键词

### NOC-2102: 误操作ai外呼导致大批量客诉
- **原因**: 车后大市场同学提交一次性ai外呼任务，外呼话术给司机后引起司机批量客诉（因为司机的状态是正常的）
- **排除理由**: 未找到微服务相关关键词

### NOC-2099: 客服进线掉底
- **原因**: 第三方兆维异常，引发客服进线异常，兆维故障原因待同步
- **排除理由**: 未找到微服务相关关键词

### NOC-2097: 小拉业务误删除永久封禁司机配置
- **原因**: 客服同学不熟悉panel平台功能，原本是想解封单个司机ID，点错按钮导致之前封禁处置的数据逆向解封，影响之前永久封禁司机的策略
- **排除理由**: 未找到微服务相关关键词

### NOC-2093: IM聊天功能异常
- **原因**: 近期上线的7.0.21客户端版本有问题，7.0.21版本用户登录过期或者切换账号之后会打开一键登录页面，用户使用一键登录功能进行登录之后，android端取错了后端返回的手机号字段保存到了本地，导致用...
- **排除理由**: 虽有微服务关键词但主要是: 客户端

### NOC-2090: 小拉大盘异常增长
- **原因**: 英雄联盟世界总决赛比赛结束，群体用户用车需求上涨。
- **排除理由**: 未找到微服务相关关键词

### NOC-2084: 部分搬家小哥收不到订单推送
- **原因**: 10月22号 18:30分左右修改OAS调度后台搬家分单配置，导致队员身份的搬家小哥晚30s收到订单推送（如果30s已成单则收不到推送）
- **排除理由**: 未找到微服务相关关键词

### NOC-2083: 小B用户企业认证异常
- **原因**: 财务侧管理的天眼查账号余额不足，“天眼查-搜索”接口无法使用，导致新增的小B用户无法完成企业认证
- **排除理由**: 未找到微服务相关关键词

### NOC-2081: 司机无法人脸识别登陆
- **原因**: 端上旷视sdk校验app包名过期导致，旷视后端有一个鉴权校验，检验发起人脸请求来自的app包名，app包名后台配置的有效期至9月30号，到10月1号凌晨就会失效导致出现问题。
ps: 不是所有都有问题...
- **排除理由**: 虽有微服务关键词但主要是: 旷视sdk

### NOC-2078: 官网大货车企业版二维码扫描无结果
- **原因**: 官网“大货车”菜单里企业app二维码扫描无结果：
22年的技术改造对老系统进行下线，对扫码下载企业app的二维码链接进行了替换，但遗漏了官网“大货车”菜单，原二维码请求流量较低（7天无请求），监控未识...
- **排除理由**: 未找到微服务相关关键词

### NOC-2071: 小拉待匹配下跌
- **原因**: 初步排查结论，阿里云物理机故障，部署在其上的ES服务受影响，抢单大厅推单、拉单应用（xl-ai-geo-svc）依赖该ES集群，从而受到影响：
本次自动恢复时间较长，主要原因是：在一个节点出现故障并自...
- **排除理由**: 虽有微服务关键词但主要是: 物理机

### NOC-2062: br bme-account-svc Exception上涨
- **原因**: 经过排查确认是 **外部攻击者使用了1万+的IP地址在验证邮箱账**号是否存在，安全研发通过新增waf规则进行拦截后恢复，保持关注中。
- **排除理由**: 虽有微服务关键词但主要是: 外部攻击

### NOC-2060: sg canal异常报错
- **原因**: aws aurora数据库证书过期导致，目前未接入dal的db集群已全部完成证书更新（已接入dal的不需要更新证书）
- **排除理由**: 未找到微服务相关关键词

### NOC-2052: 阿里云ECS宿主机故障
- **原因**: 根因为阿里云ECS实例因底层宿主机出现故障(计算NC 电源故障，11:17冷迁移)重启导致
- **排除理由**: 未找到微服务相关关键词

### NOC-2045: 兆维机房宕机
- **原因**: 兆维所在联通机房网络故障
- **排除理由**: 未找到微服务相关关键词

### NOC-2043: 客服反馈河北省部分地区司机查看不到费用明细
- **原因**: 移动运营商网络问题
- **排除理由**: 未找到微服务相关关键词

### NOC-2041: 货运大盘下单数下跌
- **原因**: 服务发布慢导致rate  计算的时候，在指定时间窗口内的每秒平均速率计算会有点问题

- **排除理由**: 未找到微服务相关关键词

### NOC-2036: 个推服务异常上涨
- **原因**: 第三方个推服务异常，具体根因为第三方个推服务 os线路收到网络攻击引起的丢包现象
![image.png](https://noc-api.huolala.cn/v1/file/17217282408...
- **排除理由**: 虽有微服务关键词但主要是: 个推服务

### NOC-2030: 阿里云到人社部电信专线丢包率 大于10%
- **原因**: 对端北京运营商网络异常
- **排除理由**: 未找到微服务相关关键词


## ✅ 微服务相关案例 (83条)

### NOC-2187: 小拉大盘支付下跌
- **包含理由**: 匹配微服务关键词: 降级

### NOC-2186: 小拉服务xl-lbs-driving-public-api异常上升
- **包含理由**: 匹配微服务关键词: 限流, xl-lbs-driving-public-api

### NOC-2185: 印度KongSync访问KongShell持续500
- **包含理由**: 匹配微服务关键词: kong, 数据库连接

### NOC-2183: bme-hpay-payment-svc服务异常上涨
- **包含理由**: 匹配微服务关键词: 服务异常, 渠道故障, bme-hpay-payment-svc

### NOC-2182: ud计价异常
- **包含理由**: 匹配微服务关键词: 超时, 降级

### NOC-2180: Cloudflare无法解析源主机名
- **包含理由**: 匹配微服务关键词: 降级

### NOC-2178: 小拉支付数下跌
- **包含理由**: 匹配微服务关键词: redis

### NOC-2173: 小拉虚拟号跌底
- **包含理由**: 匹配微服务关键词: 降级

### NOC-2170: 地图xl-map-open-api服务异常上涨
- **包含理由**: 匹配微服务关键词: 服务异常, redis, xl-map-open-api

### NOC-2167: 司机无法预约、约课，签到不了
- **包含理由**: 匹配微服务关键词: risk-driver-register-svc

... 还有73条微服务相关案例
