#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import re

def is_microservice_related(case):
    """严格判断是否为微服务调用相关问题"""
    
    # 获取案例信息
    case_id = case.get('ID')
    description = case.get('事件描述', '')
    cause = case.get('事件原因', '')
    measures = case.get('应急措施', '')
    
    # 合并所有文本
    full_text = f"{description} {cause} {measures}".lower()
    
    # 明确不是微服务问题的关键词 - 只有在没有微服务关键词时才排除
    non_microservice_keywords = [
        '客户端', '华为', 'android', 'ios', '手机', 'app版本', '客户端版本',
        'cdn故障', '阿里云cdn', '边缘节点', 'oss', '对象存储',
        '运营配置错误', '运营策略', '活动配置', '运营变更',
        '网络故障', '专线故障', '运营商网络', '机房宕机', '光缆',
        '硬件故障', '宿主机硬件', '物理机', '内存故障',
        '数据库证书', '证书过期', 'ssl证书',
        '外部攻击', '白帽扫描', '安全扫描', 'waf',
        '第三方sdk', '旷视sdk', '个推服务', '天眼查'
    ]
    
    # 微服务相关的关键词
    microservice_keywords = [
        'rpc调用', 'api调用', '服务调用', '接口调用', '服务异常', '接口异常',
        '服务间', '上游服务', '下游服务', '依赖服务', '调用链',
        '超时', '熔断', '限流', '降级', '重试', '兜底',
        '服务发现', '注册中心', 'consul', '服务注册',
        '负载均衡', '流量分配', '流量切换',
        '网关', 'gateway', 'kong', 'lapi',
        '消息队列', 'mq', 'kafka', 'rmq',
        'redis', '缓存', '数据库连接', '连接池',
        '微服务', 'soa', 'dubbo', 'grpc',
        '支付渠道', '第三方渠道', '渠道异常', '渠道故障',
        '服务重启', '服务扩容', '服务回滚', 'pod重启',
        'rds异常', 'mysql', 'mongodb', '数据库异常'
    ]

    # 检查是否包含微服务关键词
    has_microservice_keyword = False
    matched_keywords = []
    for keyword in microservice_keywords:
        if keyword in full_text:
            has_microservice_keyword = True
            matched_keywords.append(keyword)

    # 特殊情况：服务名称模式
    service_patterns = [
        r'[a-zA-Z0-9-]+-svc',
        r'[a-zA-Z0-9-]+-api',
        r'[a-zA-Z0-9-]+-service'
    ]

    service_matches = []
    for pattern in service_patterns:
        matches = re.findall(pattern, full_text)
        service_matches.extend(matches)

    if service_matches:
        has_microservice_keyword = True
        matched_keywords.extend(service_matches)

    # 如果有微服务关键词，再检查是否有排除关键词
    if has_microservice_keyword:
        # 检查是否包含非微服务关键词
        for keyword in non_microservice_keywords:
            if keyword in full_text:
                return False, f"虽有微服务关键词但主要是: {keyword}"
        return True, f"匹配微服务关键词: {', '.join(matched_keywords[:3])}"
    else:
        return False, "未找到微服务相关关键词"

def main():
    # 读取原始数据
    with open('test/noc稳定性事件24.07.10-25.07.01.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    cases = data[0]
    
    # 分析每个案例
    microservice_cases = []
    non_microservice_cases = []
    
    for case in cases:
        is_ms, reason = is_microservice_related(case)
        
        case_info = {
            'ID': case.get('ID'),
            'description': case.get('事件描述'),
            'cause': case.get('事件原因', '')[:100] + '...' if len(case.get('事件原因', '')) > 100 else case.get('事件原因', ''),
            'reason': reason
        }
        
        if is_ms:
            microservice_cases.append(case_info)
        else:
            non_microservice_cases.append(case_info)
    
    # 生成验证报告
    report = f"""# 微服务案例验证报告

## 📊 验证结果统计
- **总事件数**: {len(cases)}
- **微服务相关**: {len(microservice_cases)}条
- **非微服务相关**: {len(non_microservice_cases)}条

## ❌ 非微服务相关案例 ({len(non_microservice_cases)}条)

"""
    
    for case in non_microservice_cases:
        report += f"### NOC-{case['ID']}: {case['description']}\n"
        report += f"- **原因**: {case['cause']}\n"
        report += f"- **排除理由**: {case['reason']}\n\n"
    
    report += f"\n## ✅ 微服务相关案例 ({len(microservice_cases)}条)\n\n"
    
    for case in microservice_cases[:10]:  # 只显示前10个
        report += f"### NOC-{case['ID']}: {case['description']}\n"
        report += f"- **包含理由**: {case['reason']}\n\n"
    
    if len(microservice_cases) > 10:
        report += f"... 还有{len(microservice_cases) - 10}条微服务相关案例\n"
    
    # 保存报告
    with open('microservice_validation_report.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"验证完成！")
    print(f"微服务相关: {len(microservice_cases)}条")
    print(f"非微服务相关: {len(non_microservice_cases)}条")
    print(f"详细报告已保存到 microservice_validation_report.md")

if __name__ == "__main__":
    main()
