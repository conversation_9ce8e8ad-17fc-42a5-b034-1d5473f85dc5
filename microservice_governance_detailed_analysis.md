# 微服务治理能力使用分析报告

## 📊 总体统计

- **微服务相关事件总数**: 121
- **使用治理能力**: 34条 (28.1%)
- **未使用治理能力**: 87条 (71.9%)

## 🔧 治理能力使用分布

- **降级**: 29次 (24.0%)
- **故障转移**: 3次 (2.5%)
- **超时**: 2次 (1.7%)
- **限流**: 1次 (0.8%)
- **重试**: 1次 (0.8%)

## 📋 问题类型分布

- **其他**: 59条 (48.8%)
- **第三方依赖**: 20条 (16.5%)
- **容器/K8s**: 14条 (11.6%)
- **配置问题**: 13条 (10.7%)
- **数据库连接**: 11条 (9.1%)
- **缓存问题**: 10条 (8.3%)
- **网关问题**: 4条 (3.3%)
- **RPC调用**: 4条 (3.3%)
- **消息队列**: 4条 (3.3%)
- **服务发现**: 1条 (0.8%)

## 🎯 重点案例分析

### ✅ 使用了治理能力的案例

**NOC-2187 (隐患)**: 小拉大盘支付下跌
- 治理能力: 降级
- 应急措施: 进行紧急降级...

**NOC-2182 (风险)**: ud计价异常
- 治理能力: 降级
- 应急措施: 研发进行降级飞书发送消息功能，服务随后开始恢复正常。...

**NOC-2180 (隐患)**: Cloudflare无法解析源主机名
- 治理能力: 降级
- 应急措施: 1、对cloudflare进行降级
2、网络工程师通过调整网络配置的方式，让终端用户访问其他（雅加达以外）PoP节点，故障得以恢复...

**NOC-2173 (隐患)**: 小拉虚拟号跌底
- 治理能力: 降级
- 应急措施: 紧急降级到真实号码...

**NOC-2158 (隐患)**: 小拉支付渠道异常
- 治理能力: 降级
- 应急措施: 执行降级预案...

**NOC-2157 (隐患)**: 检索引擎服务异常导致地址检索有结果率下降
- 治理能力: 降级
- 应急措施: 1、09:36分，执行预案降级到多图商
2、10:22分，研发申请对map-textsearch-svc服务扩容 14->18...

**NOC-2153 (隐患)**: 支付渠道异常
- 治理能力: 降级
- 应急措施: 降级第三方货运通联间连渠道...

**NOC-2148 (风险)**: ai-push-api服务异常上升
- 治理能力: 限流
- 应急措施: 对服务进行扩容解决
调整调用lbs获取多语言接口的限流阈值： 70 qps --> 100 qps...

**NOC-2142 (风险)**: AWS新加坡机房的专线异常
- 治理能力: 降级, 故障转移
- 应急措施: 切换备用线路解决...

**NOC-2141 (风险)**: 客服上报新司机合同无法签署，退出协议白屏
- 治理能力: 降级
- 应急措施: 重启中转服务，降级电子合同，并引导走纸质合同...

**NOC-2138 (风险)**: 货运服务lbs-map-svc异常升高
- 治理能力: 降级
- 应急措施: 执行地图检索降级到多图商预案...

**NOC-2136 (隐患)**: 小拉支付成功数抖动
- 治理能力: 降级
- 应急措施: 对小拉连连支付渠道进行降级...

**NOC-2134 (风险)**: 货运司机准出服务RT上涨
- 治理能力: 降级
- 应急措施: 1、risk-driver-exit-svc zone1扩容2个pod (2-->4)
2、对异常pod 进行重启
3、对【校验司机是否申请退出平台】做了降级，先不判断，恢复后对降级进行了回滚...

**NOC-2132 (隐患)**: 骑手反馈工单无法填写取件码
- 治理能力: 降级
- 应急措施: 1.骑手收取件码功能降级，待产品内部确认后执行
2.用户小程序上周已修复并发布新版本，当前灰度放量中，预计今晚全量
...

**NOC-2131 (风险)**: 计价算路调用百度大车RT超时
- 治理能力: 降级, 故障转移
- 应急措施: 地图研发通过把百度切换到高德后恢复...

**NOC-2129 (隐患)**: 支付成功数下跌
- 治理能力: 降级
- 应急措施: 紧急降级通联支付渠道，渠道恢复后逐步分批次回滚降级策略...

**NOC-2120 (冒烟)**: 攻防演练导致司机成功登录数上涨
- 治理能力: 降级
- 应急措施: 风控降级人脸识别策略...

**NOC-2118 (风险)**: 外呼IVR身份识别接口QPS掉底
- 治理能力: 降级
- 应急措施: 1、兆维侧反馈已切至备用线路，无效
2、尝试切内网线路，同时csc权限删除【外网角色权限】后，可正常签入话机
3、兆维联通机房故障处理完后，恢复正常
...

**NOC-2106 (风险)**: bme-information-fee-svc zone2 rt上涨
- 治理能力: 超时
- 应急措施: 1、ai-orderhall-api调整超时时间（从500毫秒到1秒）
2、zone1已增加node节点
3、bme-information-fee-svc的zone1由100台扩容到300台
4、目前在调整 jetcache大小， 中午重新发布zone2环境
5、晚上发布zone1环境和缩容...

**NOC-2101 (风险)**: TOC服务异常上涨RMQ内存水位上涨
- 治理能力: 降级
- 应急措施: 1）通过降级临时任务恢复
2）研发通过临时修改toc问题任务配置临时解决
3）对bme-trade-freight-core-svc进行少量扩容
4）后续计划下周一发布代码避免重复发生
...


### ❌ 未使用治理能力的典型案例

**NOC-2192 (隐患)**: 用户app下单超时未支付
- 问题类型: 第三方依赖
- 应急措施: 引导用户重装app或去微信小程序支付...

**NOC-2191 (风险)**: 阿里云CDN故障
- 问题类型: 第三方依赖
- 应急措施: 阿里云侧对异常节点进行下线...

**NOC-2190 (风险)**: lbs-driving-svc 异常上涨
- 问题类型: 其他
- 应急措施: 1、恢复方式为摘除异常节点
2、下线一个CH引擎节点，批量算路切直线...

**NOC-2189 (风险)**: 小拉抢单数持续上涨
- 问题类型: 其他
- 应急措施: NOC拉起事件通过系统监控和排查，最终确认为运营调价策略调整所致。...

**NOC-2188 (风险)**: hestia异常上涨导致营销活动发奖失败
- 问题类型: 配置问题
- 应急措施: DBA通过DMS数据闪回技术，成功找回被误更新的数据，并在17:10分使服务恢复正常。...

**NOC-2186 (风险)**: 小拉服务xl-lbs-driving-public-api异常上升
- 问题类型: 容器/K8s
- 应急措施: 通过紧急扩容后恢复...

**NOC-2185 (风险)**: 印度KongSync访问KongShell持续500
- 问题类型: 网关问题, 数据库连接
- 应急措施: 1. DBA通过备份快照恢复较低版本(PG12)的数据库；
2. Kong团队重新部署Kong节点并更新数据库连接串；
3. 分批对外网和内网Kong节点进行配置更新和重启，最终恢复服务；
4. 对UD进行临时封网。...

**NOC-2184 (风险)**: 多例搬家小哥无法自主叫车
- 问题类型: 配置问题
- 应急措施: 进行回滚配置操作...

**NOC-2183 (风险)**: bme-hpay-payment-svc服务异常上涨
- 问题类型: 其他
- 应急措施: 自动恢复...

**NOC-2178 (隐患)**: 小拉支付数下跌
- 问题类型: 数据库连接, 缓存问题, 第三方依赖
- 应急措施: 异常节点摘除后大盘恢复...

**NOC-2172 (风险)**: 小拉下单数抖动
- 问题类型: 配置问题
- 应急措施: 更正为正常的apollo配置...

**NOC-2170 (风险)**: 地图xl-map-open-api服务异常上涨
- 问题类型: 数据库连接, 缓存问题
- 应急措施: 通过重启相关地图服务恢复...

**NOC-2169 (风险)**: 司机侧完成一笔订单就会收到“违规收费”警告
- 问题类型: 其他
- 应急措施: 对stream系统-决策中心-受理动作配置的完单警告策略进行下线操作...

**NOC-2168 (风险)**: br多名司机处于账号封停状态
- 问题类型: 其他
- 应急措施: 回滚为php守护进程，恢复数据...

**NOC-2167 (风险)**: 司机无法预约、约课，签到不了
- 问题类型: 其他
- 应急措施: 停止司库侧刷数任务，并引导一线人员让司机重新操作。...

