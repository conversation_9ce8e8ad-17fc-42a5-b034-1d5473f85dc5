#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import re
from datetime import datetime, timedelta
from collections import defaultdict

def parse_datetime(dt_str):
    """解析日期时间字符串"""
    try:
        # 尝试不同的日期格式
        formats = [
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%d %H:%M',
            '%Y-%m-%d',
            '%m/%d/%Y %H:%M',
            '%m/%d/%Y'
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(dt_str, fmt)
            except ValueError:
                continue
        
        # 如果都不匹配，返回None
        return None
    except:
        return None

def extract_app_id_from_text(text):
    """从文本中提取可能的app_id"""
    if not text:
        return []

    # 常见的服务名模式
    patterns = [
        r'([a-zA-Z0-9-]+)-svc',
        r'([a-zA-Z0-9-]+)-api',
        r'([a-zA-Z0-9-]+)\.service',
        r'([a-zA-Z0-9-]+)服务',
        r'([a-zA-Z0-9-]+)接口',
        r'([a-zA-Z0-9-]+)-public-api',
        r'([a-zA-Z0-9-]+)-core-svc',
        r'([a-zA-Z0-9-]+)-payment-svc',
        r'([a-zA-Z0-9-]+)-driving-svc',
        r'([a-zA-Z0-9-]+)-map-open-api',
        r'([a-zA-Z0-9-]+)-orderhall-api',
        r'bme-([a-zA-Z0-9-]+)',
        r'xl-([a-zA-Z0-9-]+)',
        r'lbs-([a-zA-Z0-9-]+)',
        r'ai-([a-zA-Z0-9-]+)',
        r'ud([a-zA-Z0-9-]*)',
        r'kong([a-zA-Z0-9-]*)'
    ]

    app_ids = []
    for pattern in patterns:
        matches = re.findall(pattern, text.lower())
        app_ids.extend(matches)

    # 直接查找可能的完整服务名
    service_patterns = [
        r'([a-zA-Z0-9-]+(?:-svc|-api|-service))',
        r'(bme-[a-zA-Z0-9-]+)',
        r'(xl-[a-zA-Z0-9-]+)',
        r'(lbs-[a-zA-Z0-9-]+)',
        r'(ai-[a-zA-Z0-9-]+)'
    ]

    for pattern in service_patterns:
        matches = re.findall(pattern, text.lower())
        app_ids.extend(matches)

    return list(set(app_ids))

def check_governance_in_text(text):
    """检查文本中是否明确提到执行了治理预案"""
    if not text:
        return False, []
    
    governance_keywords = [
        '执行.*降级.*预案', '执行.*限流.*预案', '执行.*超时.*预案',
        '启动.*降级.*预案', '启动.*限流.*预案', '启动.*超时.*预案',
        '触发.*降级.*预案', '触发.*限流.*预案', '触发.*超时.*预案',
        '进行.*降级', '进行.*限流', '调整.*超时',
        '降级.*处理', '限流.*处理', '超时.*处理',
        '应急.*降级', '应急.*限流', '应急.*超时'
    ]
    
    found_keywords = []
    for keyword in governance_keywords:
        if re.search(keyword, text):
            found_keywords.append(keyword)
    
    return len(found_keywords) > 0, found_keywords

def find_matching_operations(case, xl_ops, hy_ops):
    """查找事件时间范围内的匹配操作记录"""

    # 解析事件时间 - 使用正确的字段名
    start_time_str = case.get('发生时间', '') or case.get('发现时间', '')
    end_time_str = case.get('恢复时间', '') or case.get('止血时间', '')

    start_time = parse_datetime(start_time_str)
    end_time = parse_datetime(end_time_str)
    
    if not start_time or not end_time:
        return []
    
    # 从事件描述和原因中提取可能的app_id
    text_content = f"{case.get('事件描述', '')} {case.get('事件原因', '')} {case.get('应急措施', '')}"
    possible_app_ids = extract_app_id_from_text(text_content)
    
    # 直接使用事件中的appid
    if case.get('appid'):
        possible_app_ids.append(case.get('appid'))
    
    matching_ops = []
    
    # 检查小拉操作记录
    for op in xl_ops:
        op_time = parse_datetime(op.get('created_at', ''))
        if not op_time:
            continue
            
        if start_time <= op_time <= end_time:
            # 检查app_id是否匹配 - 更宽松的匹配逻辑
            op_app_id = op.get('app_id', '').lower()
            matched = False

            # 直接匹配appid字段
            case_appid = case.get('appid', '').lower()
            if case_appid and case_appid in op_app_id:
                matching_ops.append({
                    'source': '小拉',
                    'operation': op,
                    'match_reason': f'直接appid匹配: {case_appid} <-> {op_app_id}'
                })
                matched = True

            # 从文本提取的可能app_id匹配
            if not matched:
                for possible_id in possible_app_ids:
                    if len(possible_id) >= 3:  # 避免太短的匹配
                        if possible_id in op_app_id or op_app_id in possible_id:
                            matching_ops.append({
                                'source': '小拉',
                                'operation': op,
                                'match_reason': f'文本提取匹配: {possible_id} <-> {op_app_id}'
                            })
                            break
    
    # 检查货运操作记录
    for op in hy_ops:
        op_time = parse_datetime(op.get('created_at', ''))
        if not op_time:
            continue
            
        if start_time <= op_time <= end_time:
            # 检查app_id是否匹配 - 更宽松的匹配逻辑
            op_app_id = op.get('app_id', '').lower()
            matched = False

            # 直接匹配appid字段
            case_appid = case.get('appid', '').lower()
            if case_appid and case_appid in op_app_id:
                matching_ops.append({
                    'source': '货运',
                    'operation': op,
                    'match_reason': f'直接appid匹配: {case_appid} <-> {op_app_id}'
                })
                matched = True

            # 从文本提取的可能app_id匹配
            if not matched:
                for possible_id in possible_app_ids:
                    if len(possible_id) >= 3:  # 避免太短的匹配
                        if possible_id in op_app_id or op_app_id in possible_id:
                            matching_ops.append({
                                'source': '货运',
                                'operation': op,
                                'match_reason': f'文本提取匹配: {possible_id} <-> {op_app_id}'
                            })
                            break
    
    return matching_ops

def main():
    # 读取所有文件
    with open('test/noc稳定性事件24.07.10-25.07.01.json', 'r', encoding='utf-8') as f:
        incidents_data = json.load(f)
    
    with open('test/soa admin小拉操作记录.json', 'r', encoding='utf-8') as f:
        xl_operations = json.load(f)
    
    with open('test/soa admin货运操作记录.json', 'r', encoding='utf-8') as f:
        hy_operations = json.load(f)
    
    cases = incidents_data[0]
    
    # 重新分析每个案例
    updated_cases = []
    governance_stats = {
        'original_governance': 0,
        'text_governance': 0,
        'operation_governance': 0,
        'total_governance': 0
    }
    
    for case in cases:
        case_id = case.get('ID')
        
        # 检查原有的治理能力使用
        original_governance = False
        text_content = f"{case.get('事件描述', '')} {case.get('事件原因', '')} {case.get('应急措施', '')}"
        
        # 原有的治理能力检查
        governance_keywords = ['降级', '限流', '熔断', '重试', '超时', '兜底', '切换', '故障转移']
        if any(keyword in case.get('应急措施', '').lower() for keyword in governance_keywords):
            original_governance = True
            governance_stats['original_governance'] += 1
        
        # 新条件1: 检查文本中是否明确提到执行了治理预案
        has_text_governance, governance_keywords_found = check_governance_in_text(text_content)
        if has_text_governance:
            governance_stats['text_governance'] += 1
        
        # 新条件2: 检查操作记录
        matching_operations = find_matching_operations(case, xl_operations, hy_operations)
        has_operation_governance = len(matching_operations) > 0
        if has_operation_governance:
            governance_stats['operation_governance'] += 1
        
        # 综合判断是否使用了治理能力
        used_governance = original_governance or has_text_governance or has_operation_governance
        if used_governance:
            governance_stats['total_governance'] += 1
        
        # 更新案例信息
        updated_case = case.copy()
        updated_case['governance_analysis'] = {
            'original_governance': original_governance,
            'text_governance': has_text_governance,
            'text_governance_keywords': governance_keywords_found,
            'operation_governance': has_operation_governance,
            'matching_operations': matching_operations,
            'final_used_governance': used_governance
        }
        
        updated_cases.append(updated_case)
    
    # 生成更新后的分析报告
    total_cases = len(cases)
    
    print(f"治理能力使用情况统计:")
    print(f"- 原有治理能力: {governance_stats['original_governance']}条")
    print(f"- 文本明确提及治理预案: {governance_stats['text_governance']}条")
    print(f"- 操作记录匹配: {governance_stats['operation_governance']}条")
    print(f"- 总计使用治理能力: {governance_stats['total_governance']}条 ({governance_stats['total_governance']/total_cases*100:.1f}%)")
    
    # 保存详细分析结果
    with open('updated_governance_analysis.json', 'w', encoding='utf-8') as f:
        json.dump({
            'statistics': governance_stats,
            'cases': updated_cases
        }, f, ensure_ascii=False, indent=2)
    
    return updated_cases, governance_stats

if __name__ == "__main__":
    updated_cases, stats = main()
