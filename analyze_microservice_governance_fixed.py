#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
微服务治理工作成果分析脚本（修正版）
"""

import json
from datetime import datetime, timedelta
import re
from collections import defaultdict

def check_governance_mentioned_in_event(event):
    """检查事件记录中是否明确提到了治理操作"""
    event_desc = event.get('事件描述', '')
    event_process = event.get('事件过程', '')
    emergency_measures = event.get('应急措施', '')
    event_cause = event.get('事件原因', '')
    event_risk = event.get('事件风险(暴露的问题)', '')
    
    # 重点检查应急措施字段
    emergency_text = emergency_measures.lower()
    
    # 检查是否明确提到了治理操作
    governance_keywords = [
        '降级', '限流', '超时控制', '熔断', '治理',
        'degrade', 'limit', 'timeout', 'circuit',
        '服务降级', '接口降级', '流量限制', '超时控制',
        'soa admin', '控制面', '治理手段', '紧急降级',
        '进行降级', '执行降级', '降级操作', '限流操作',
        '执行预案', '降级预案', '预案', '切流', '回滚',
        '调整.*限流', '调整.*超时', '超时时间'
    ]
    
    mentioned_actions = []
    governance_details = {}
    
    # 检查应急措施中的治理操作
    for keyword in governance_keywords:
        if keyword in emergency_text:
            mentioned_actions.append(keyword)
            # 记录具体的应急措施内容
            if keyword not in governance_details:
                governance_details[keyword] = []
            governance_details[keyword].append(f"应急措施: {emergency_measures}")
    
    # 也检查其他字段
    other_text = f"{event_desc} {event_process} {event_cause} {event_risk}".lower()
    for keyword in governance_keywords:
        if keyword in other_text and keyword not in mentioned_actions:
            mentioned_actions.append(keyword)
            if keyword not in governance_details:
                governance_details[keyword] = []
            if keyword in event_process.lower():
                governance_details[keyword].append(f"事件过程: {event_process}")
    
    return mentioned_actions, governance_details

def load_json_data(file_path):
    """加载JSON数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载文件 {file_path} 失败: {e}")
        return []

def parse_time(time_str):
    """解析时间字符串"""
    if not time_str:
        return None
    
    try:
        # 尝试解析常见的时间格式
        for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M:%S.%f', '%Y/%m/%d %H:%M:%S']:
            try:
                return datetime.strptime(time_str, fmt)
            except ValueError:
                continue
        return None
    except Exception:
        return None

def classify_stability_events(events):
    """对稳定性事件进行分类"""
    categories = {
        '微服务问题': [],  # 可以通过SOA admin治理手段影响的问题
        '容器问题': [],
        '网络问题': [],
        '基础设施问题': [],
        '第三方问题': [],
        '配置问题': [],
        '代码问题': [],
        '运营问题': [],
        '其他问题': []
    }
    
    for event in events:
        event_id = event.get('ID')
        event_desc = event.get('事件描述', '')
        root_cause = event.get('根因类型', '')
        event_cause = event.get('事件原因', '')
        responsible_dept = event.get('责任部门', '')
        app_id = event.get('appid', '')
        
        # 微服务问题：专注于RPC服务调用相关的问题，可以通过SOA admin治理手段影响
        # 根据记忆：微服务问题应该是可以通过治理措施影响的，专注于RPC接口调用（非HTTP）
        # 并且需要忽略demo类型的服务
        is_microservice_problem = False
        
        # 1. 明确包含微服务名称的事件（-svc, -api结尾的服务，排除demo）
        if any(keyword in event_desc.lower() for keyword in [
            '-svc', '-api', 'service-svc', 'facade-svc'
        ]) and 'demo' not in event_desc.lower():
            is_microservice_problem = True
        
        # 2. 明确的RPC服务调用问题（排除HTTP相关）
        elif (any(keyword in event_desc.lower() for keyword in [
            'rpc', '服务调用', '服务异常', '服务抖动', 'rt上涨', 'rt增长', 
            '超时', 'timeout', 'svc异常', 'api异常', '调用超时'
        ]) and not any(exclude in event_desc.lower() for exclude in [
            'http', 'cdn', '地图api', '支付宝', '微信支付', '百度地图', '阿里云', '腾讯云',
            'map-open-api', 'open-api', 'web', 'h5', '前端'
        ])):
            is_microservice_problem = True
        
        # 3. 有明确的微服务appid（排除demo和第三方服务）
        elif (app_id and 
              any(suffix in app_id.lower() for suffix in ['-svc', '-api', 'service']) and 
              'demo' not in app_id.lower() and
              not any(exclude in app_id.lower() for exclude in ['map-open', 'open-api', 'web-'])):
            is_microservice_problem = True
        
        # 4. 明确的微服务RPC调用相关根因
        elif any(keyword in root_cause.lower() for keyword in [
            '服务调用', 'rpc调用', '接口超时', '服务异常', '微服务'
        ]) and 'http' not in root_cause.lower():
            is_microservice_problem = True
        
        if is_microservice_problem:
            categories['微服务问题'].append(event)
        # 容器问题
        elif any(keyword in root_cause.lower() or keyword in event_desc.lower() or keyword in event_cause.lower() 
               for keyword in ['容器', 'container', 'docker', 'k8s', 'kubernetes', 'pod', 'cpu', '内存', '磁盘']):
            categories['容器问题'].append(event)
        # 网络问题
        elif any(keyword in root_cause.lower() or keyword in event_desc.lower() or keyword in event_cause.lower()
                for keyword in ['网络', 'network', 'cdn', 'dns', 'cloudflare', '专线', '丢包']):
            categories['网络问题'].append(event)
        # 基础设施问题
        elif any(keyword in root_cause.lower() or keyword in event_desc.lower() or keyword in event_cause.lower()
                for keyword in ['基础设施', '云资源', 'rds', 'redis', '机房', '宕机', 'ecs', 'lvs', '故障']):
            categories['基础设施问题'].append(event)
        # 第三方问题
        elif ('第三方' in responsible_dept or 
              any(keyword in root_cause.lower() or keyword in event_desc.lower() for keyword in [
                  '第三方', '外部服务', '运营商', '阿里云', '腾讯云', 'aws', '百度', '微信', '支付宝'
              ])):
            categories['第三方问题'].append(event)
        # 配置问题
        elif any(keyword in root_cause.lower() or keyword in event_desc.lower() or keyword in event_cause.lower()
                for keyword in ['配置', 'config', 'apollo', '策略', '误删', '误操作']):
            categories['配置问题'].append(event)
        # 代码问题
        elif any(keyword in root_cause.lower() or keyword in event_desc.lower() for keyword in ['代码', 'bug', 'code', '埋点', '数据量缺失']):
            categories['代码问题'].append(event)
        # 运营问题
        elif any(keyword in root_cause.lower() or keyword in responsible_dept.lower() or keyword in event_desc.lower()
                for keyword in ['运营', '产品', '客诉', '审核', '工单']):
            categories['运营问题'].append(event)
        else:
            categories['其他问题'].append(event)
    
    return categories

def analyze_microservice_event(event, all_operations):
    """分析单个微服务事件的治理情况"""
    event_id = event.get('ID')
    event_desc = event.get('事件描述', '')
    event_time_str = event.get('发生时间', '')
    recovery_time_str = event.get('恢复时间', '')
    business_line = event.get('影响业务线', '')
    app_id = event.get('appid', '')
    emergency_measures = event.get('应急措施', '')
    
    event_time = parse_time(event_time_str)
    recovery_time = parse_time(recovery_time_str)
    
    # 查找事件发生到恢复时间之间的SOA操作（治理操作必须在此期间才有效）
    if recovery_time and event_time:
        time_window_start = event_time  # 事件发生时间
        time_window_end = recovery_time  # 事件恢复时间
    else:
        # 如果没有恢复时间，则查找事件发生后2小时内的操作
        time_window_start = event_time
        time_window_end = event_time + timedelta(hours=2) if event_time else None
    
    related_operations = []
    if time_window_start and time_window_end:
        for op in all_operations:
            op_time = parse_time(op.get('created_at') or op.get('updated_at'))
            if op_time and time_window_start <= op_time <= time_window_end:
                # 过滤demo类型的服务
                op_app_id = op.get('app_id', '')
                if 'demo' in op_app_id.lower():
                    continue
                
                # 如果事件有明确的appid，优先匹配相同或相关的服务
                if app_id:
                    # 精确匹配或包含关系
                    if (app_id == op_app_id or 
                        app_id in op_app_id or 
                        op_app_id in app_id or
                        # 同一服务族的匹配（如bme-trade-xxx系列）
                        (len(app_id.split('-')) >= 2 and len(op_app_id.split('-')) >= 2 and
                         '-'.join(app_id.split('-')[:2]) == '-'.join(op_app_id.split('-')[:2]))):
                        related_operations.append(op)
                else:
                    # 没有明确appid的事件，记录时间窗口内的所有治理操作
                    related_operations.append(op)
    
    # 分析是否有治理操作
    governance_actions = []
    for op in related_operations:
        op_type = op.get('type', '')
        if op_type in ['DEGRADE', 'TIMEOUT', 'LIMIT']:
            governance_actions.append(op)
    
    # 检查事件记录中是否明确提到了治理操作
    event_mentioned_governance, governance_details = check_governance_mentioned_in_event(event)
    
    # 检查应急措施中是否明确提到了治理操作
    has_governance_in_emergency = any(keyword in emergency_measures.lower() for keyword in [
        '降级', '限流', '预案', '执行预案', '降级预案', '切流', '回滚', '超时控制',
        '调整.*限流', '调整.*超时', '超时时间'
    ])
    
    # 判断是否使用了治理
    has_governance = len(governance_actions) > 0 or len(event_mentioned_governance) > 0 or has_governance_in_emergency
    
    return {
        'event_id': event_id,
        'event_desc': event_desc,
        'has_governance': has_governance,
        'governance_actions': governance_actions,
        'event_mentioned_governance': event_mentioned_governance,
        'governance_details': governance_details,
        'has_governance_in_emergency': has_governance_in_emergency,
        'emergency_measures': emergency_measures,
        'related_operations': related_operations
    }

def main():
    print("开始分析微服务治理工作成果...")
    
    # 加载数据
    print("加载稳定性事件数据...")
    events_data = load_json_data('test/noc稳定性事件24.07.10-25.07.01.json')
    events = events_data if isinstance(events_data, list) else [events_data]

    print("加载货运SOA admin操作记录...")
    freight_ops = load_json_data('test/soa admin货运操作记录.json')

    print("加载小拉SOA admin操作记录...")
    xl_ops = load_json_data('test/soa admin小拉操作记录.json')
    
    all_operations = freight_ops + xl_ops
    
    print(f"稳定性事件数量: {len(events)}")
    print(f"货运环境操作记录数量: {len(freight_ops)}")
    print(f"小拉环境操作记录数量: {len(xl_ops)}")
    print(f"总操作记录数量: {len(all_operations)}")
    
    # 对稳定性事件进行分类
    print("\n对稳定性事件进行分类...")
    event_categories = classify_stability_events(events)
    
    print("\n=== 稳定性事件分类结果 ===")
    for category, events_list in event_categories.items():
        print(f"{category}: {len(events_list)}个事件")
    
    # 分析微服务问题事件
    microservice_events = event_categories['微服务问题']
    print(f"\n开始分析{len(microservice_events)}个微服务问题事件...")
    
    analysis_results = []
    for event in microservice_events:
        result = analyze_microservice_event(event, all_operations)
        analysis_results.append(result)
    
    # 统计结果
    used_governance_count = sum(1 for result in analysis_results if result['has_governance'])
    not_used_count = len(analysis_results) - used_governance_count
    
    print(f"\n=== 统计汇总 ===")
    print(f"微服务问题事件总数: {len(analysis_results)}")
    print(f"使用了SOA admin治理: {used_governance_count}个 ({used_governance_count/len(analysis_results)*100:.1f}%)")
    print(f"未使用治理: {not_used_count}个 ({not_used_count/len(analysis_results)*100:.1f}%)")
    
    # 输出使用了治理的事件列表
    print(f"\n=== 使用了治理的事件列表 ===")
    for result in analysis_results:
        if result['has_governance']:
            print(f"ID {result['event_id']}: {result['event_desc']}")
            if result['governance_actions']:
                print(f"  - 操作日志记录: {len(result['governance_actions'])}个治理操作")
            if result['event_mentioned_governance']:
                print(f"  - 事件记录提到: {', '.join(result['event_mentioned_governance'])}")
            if result['has_governance_in_emergency']:
                print(f"  - 应急措施提到治理操作")
            print()

if __name__ == "__main__":
    main()
