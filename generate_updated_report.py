#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

def main():
    # 读取更新后的分析结果
    with open('updated_governance_analysis.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    cases = data['cases']
    stats = data['statistics']
    
    # 生成更新后的报告
    total_cases = len(cases)
    microservice_cases = []
    
    # 筛选微服务相关案例
    microservice_keywords = [
        'rpc', 'api', 'svc', 'service', '服务', '接口', '调用', 
        '超时', '异常', '限流', '降级', '熔断', '重试', 
        'kong', 'gateway', '网关', '负载均衡', '服务发现',
        '微服务', 'consul', 'dubbo', 'grpc', 'http'
    ]
    
    for case in cases:
        text_content = f"{case.get('事件描述', '')} {case.get('事件原因', '')} {case.get('应急措施', '')}"
        text_content = text_content.lower()
        
        is_microservice = any(keyword in text_content for keyword in microservice_keywords)
        if is_microservice:
            microservice_cases.append(case)
    
    # 统计治理能力使用情况
    governance_used_cases = [case for case in microservice_cases if case['governance_analysis']['final_used_governance']]
    text_governance_cases = [case for case in microservice_cases if case['governance_analysis']['text_governance']]
    operation_governance_cases = [case for case in microservice_cases if case['governance_analysis']['operation_governance']]
    
    # 生成报告
    report = f"""# 微服务治理能力使用分析更新报告

## 📊 更新后的统计数据

### 总体情况
- **事件总数**: {total_cases}条
- **微服务相关事件**: {len(microservice_cases)}条 ({len(microservice_cases)/total_cases*100:.1f}%)
- **使用治理能力事件**: {len(governance_used_cases)}条 ({len(governance_used_cases)/len(microservice_cases)*100:.1f}%)

### 治理能力使用分类
- **原有治理能力识别**: {stats['original_governance']}条
- **文本明确提及治理预案**: {stats['text_governance']}条
- **操作记录匹配**: {stats['operation_governance']}条
- **总计使用治理能力**: {stats['total_governance']}条

## 🔍 新增治理能力识别详情

### 1. 文本明确提及治理预案的案例

"""
    
    for case in text_governance_cases:
        if case['governance_analysis']['text_governance']:
            report += f"**NOC-{case['ID']}**: {case['事件描述']}\n"
            report += f"- 治理关键词: {', '.join(case['governance_analysis']['text_governance_keywords'])}\n"
            report += f"- 应急措施: {case['应急措施'][:150]}...\n\n"
    
    report += "### 2. 操作记录匹配的案例\n\n"
    
    for case in operation_governance_cases:
        if case['governance_analysis']['operation_governance']:
            report += f"**NOC-{case['ID']}**: {case['事件描述']}\n"
            report += f"- 发生时间: {case.get('发生时间', 'N/A')}\n"
            report += f"- 恢复时间: {case.get('恢复时间', 'N/A')}\n"
            
            for op_match in case['governance_analysis']['matching_operations']:
                op = op_match['operation']
                report += f"- 匹配操作({op_match['source']}): {op['type']} - {op['app_id']}\n"
                report += f"  - 操作时间: {op['created_at']}\n"
                report += f"  - 操作数据: {op['data'][:100]}...\n"
                report += f"  - 匹配原因: {op_match['match_reason']}\n"
            report += "\n"
    
    # 重点微服务问题案例更新
    report += f"""
## 🚨 重点微服务问题案例更新

### ✅ 使用治理能力的案例 (更新后: {len(governance_used_cases)}条)

"""
    
    # 显示前20个使用治理能力的案例
    for i, case in enumerate(governance_used_cases[:20], 1):
        governance_types = []
        if case['governance_analysis']['original_governance']:
            governance_types.append("原有治理")
        if case['governance_analysis']['text_governance']:
            governance_types.append("文本治理预案")
        if case['governance_analysis']['operation_governance']:
            governance_types.append("操作记录")
        
        report += f"#### {i}. NOC-{case['ID']} ({case.get('事件类型', 'N/A')})\n"
        report += f"- **问题描述**: {case['事件描述']}\n"
        report += f"- **治理能力类型**: {', '.join(governance_types)}\n"
        report += f"- **应急措施**: {case['应急措施'][:150]}...\n\n"
    
    # 保存报告
    with open('updated_microservice_governance_report.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"更新报告生成完成！")
    print(f"微服务事件: {len(microservice_cases)}条")
    print(f"使用治理能力: {len(governance_used_cases)}条 ({len(governance_used_cases)/len(microservice_cases)*100:.1f}%)")

if __name__ == "__main__":
    main()
