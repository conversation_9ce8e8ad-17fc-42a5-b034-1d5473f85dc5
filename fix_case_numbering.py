#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def fix_case_numbering():
    # 读取文件
    with open('microservice_analysis_report.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 找到所有的案例编号模式 ### 数字. NOC-
    pattern = r'### (\d+)\. (NOC-\d+)'
    matches = list(re.finditer(pattern, content))
    
    # 从后往前替换，避免位置偏移
    new_content = content
    current_number = 1
    
    for match in matches:
        old_number = match.group(1)
        noc_id = match.group(2)
        
        # 跳过NOC-2192，因为它不是微服务问题
        if noc_id == 'NOC-2192':
            continue
            
        # 替换编号
        old_text = f"### {old_number}. {noc_id}"
        new_text = f"### {current_number}. {noc_id}"
        
        new_content = new_content.replace(old_text, new_text, 1)
        current_number += 1
    
    # 移除NOC-2192整个案例
    # 找到NOC-2192的开始和结束位置
    noc_2192_start = new_content.find('### 1. NOC-2192')
    if noc_2192_start != -1:
        # 找到下一个案例的开始位置
        next_case_start = new_content.find('### 1. NOC-2191', noc_2192_start)
        if next_case_start != -1:
            # 删除NOC-2192案例
            new_content = new_content[:noc_2192_start] + new_content[next_case_start:]
    
    # 保存文件
    with open('microservice_analysis_report.md', 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"案例编号修复完成！总共处理了{current_number-1}个微服务相关案例")

if __name__ == "__main__":
    fix_case_numbering()
