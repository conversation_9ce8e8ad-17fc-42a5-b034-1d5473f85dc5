#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import re
from collections import defaultdict, Counter

def analyze_microservice_governance(case):
    """分析是否为微服务相关问题以及是否使用了治理能力"""
    
    # 微服务相关关键词
    microservice_keywords = [
        'rpc', 'api', 'svc', 'service', '服务', '接口', '调用', 
        '超时', '异常', '限流', '降级', '熔断', '重试', 
        'kong', 'gateway', '网关', '负载均衡', '服务发现',
        '微服务', 'consul', 'dubbo', 'grpc', 'http',
        '服务间', '调用链', '依赖', '上游', '下游'
    ]
    
    # 治理能力关键词
    governance_keywords = [
        '限流', '降级', '熔断', '重试', '超时', '兜底', 
        '切换', '回退', '故障转移', '负载均衡', '流量控制',
        '服务治理', '容错', '隔离', '预案', '应急切换',
        '自动切换', '备用', '降级预案', '限流预案'
    ]
    
    # 检查文本内容
    text_content = f"{case.get('事件描述', '')} {case.get('事件原因', '')} {case.get('应急措施', '')}"
    text_content = text_content.lower()
    
    # 判断是否为微服务问题
    is_microservice = any(keyword in text_content for keyword in microservice_keywords)
    
    # 判断是否使用了治理能力
    used_governance = any(keyword in case.get('应急措施', '').lower() for keyword in governance_keywords)
    
    return is_microservice, used_governance

def main():
    # 读取JSON文件
    with open('test/noc稳定性事件24.07.10-25.07.01.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    cases = data[0]  # 获取案例数组
    
    # 统计数据
    total_cases = len(cases)
    case_types = Counter()
    root_causes = Counter()
    microservice_cases = []
    governance_used_cases = []
    
    # 分析每个案例
    for case in cases:
        case_type = case.get('事件类型', '未知')
        root_cause = case.get('根因类型', '未知')
        
        case_types[case_type] += 1
        root_causes[root_cause] += 1
        
        # 分析微服务治理
        is_microservice, used_governance = analyze_microservice_governance(case)
        
        if is_microservice:
            microservice_cases.append({
                'ID': case.get('ID'),
                'description': case.get('事件描述'),
                'cause': case.get('事件原因'),
                'measures': case.get('应急措施'),
                'used_governance': used_governance
            })
            
        if used_governance:
            governance_used_cases.append({
                'ID': case.get('ID'),
                'description': case.get('事件描述'),
                'measures': case.get('应急措施')
            })
    
    # 生成分析报告
    report = f"""# 微服务稳定性事件分析报告

## 📊 总体概况
- **事件总数**: {total_cases}
- **时间范围**: 2024年7月10日 - 2025年7月1日
- **微服务相关事件**: {len(microservice_cases)}条 ({len(microservice_cases)/total_cases*100:.1f}%)
- **使用治理能力事件**: {len(governance_used_cases)}条 ({len(governance_used_cases)/total_cases*100:.1f}%)

## 📈 事件类型分布
"""
    
    for case_type, count in case_types.most_common():
        percentage = count/total_cases*100
        report += f"- **{case_type}**: {count}条 ({percentage:.1f}%)\n"
    
    report += "\n## 🔍 根因类型分布\n"
    for root_cause, count in root_causes.most_common():
        if root_cause and root_cause != '未知':
            percentage = count/total_cases*100
            report += f"- **{root_cause}**: {count}条 ({percentage:.1f}%)\n"

    # 添加关键发现
    governance_used_count = sum(1 for case in microservice_cases if case['used_governance'])
    governance_not_used_count = len(microservice_cases) - governance_used_count

    report += f"""
## 🎯 关键发现

### 微服务治理现状
- **微服务相关事件占比极高**: {len(microservice_cases)/total_cases*100:.1f}%的事件涉及微服务调用
- **治理能力使用率偏低**: 仅{governance_used_count/len(microservice_cases)*100:.1f}%的微服务事件使用了治理能力
- **主要依赖人工干预**: 大部分问题通过重启、扩容、手动降级等方式解决

### 问题分布特点
- **第三方依赖风险高**: {root_causes.get('第三方网络/外部服务-其他', 0) + root_causes.get('第三方网络/外部服务-支付渠道异常', 0) + root_causes.get('第三方网络/外部服务-运营商网络异常', 0)}条事件由第三方服务引起
- **代码质量问题突出**: {root_causes.get('变更-代码变更-BUG', 0)}条事件由代码BUG引起
- **基础设施不稳定**: {root_causes.get('基础设施-云资源异常-云主机硬件损坏', 0)}条事件由云主机硬件故障引起

## 🔧 微服务相关问题详细分析

共发现 **{len(microservice_cases)}** 条微服务相关事件：

"""
    
    # 详细分析微服务案例
    for i, case in enumerate(microservice_cases, 1):
        governance_status = "✅ 使用了治理能力" if case['used_governance'] else "❌ 未使用治理能力"
        report += f"### {i}. NOC-{case['ID']}\n"
        report += f"- **问题描述**: {case['description']}\n"
        report += f"- **根本原因**: {case['cause'][:200]}...\n" if len(case['cause']) > 200 else f"- **根本原因**: {case['cause']}\n"
        report += f"- **应急措施**: {case['measures'][:200]}...\n" if len(case['measures']) > 200 else f"- **应急措施**: {case['measures']}\n"
        report += f"- **治理能力使用**: {governance_status}\n\n"
    
    report += f"""
## 📊 治理能力使用情况统计

在{len(microservice_cases)}条微服务相关事件中：
- **使用了治理能力**: {governance_used_count}条 ({governance_used_count/len(microservice_cases)*100:.1f}%)
- **未使用治理能力**: {governance_not_used_count}条 ({governance_not_used_count/len(microservice_cases)*100:.1f}%)

### ✅ 使用治理能力的案例详情：

"""

    if governance_used_cases:
        for case in governance_used_cases:
            report += f"**NOC-{case['ID']}**: {case['description']}\n"
            report += f"治理措施: {case['measures']}\n\n"

    # 添加改进建议
    report += f"""
## 💡 改进建议

### 1. 微服务治理能力建设
- **完善熔断机制**: 对关键服务调用链路增加熔断保护
- **增强限流能力**: 建立多层次限流策略（接口级、服务级、用户级）
- **优化降级策略**: 制定标准化降级预案，提高自动降级能力
- **强化超时控制**: 统一服务间调用超时配置，避免级联故障

### 2. 第三方依赖治理
- **建立隔离机制**: 对第三方服务调用进行资源隔离
- **完善兜底方案**: 为关键第三方依赖建立备用方案
- **增强监控告警**: 提升第三方服务异常的发现速度
- **定期演练**: 建立第三方服务故障应急演练机制

### 3. 基础设施稳定性
- **多云部署**: 降低单一云厂商故障风险
- **容器化改造**: 提升服务部署和扩容效率
- **资源监控**: 完善基础资源监控和预警机制
- **故障自愈**: 建立自动故障检测和恢复能力

### 4. 代码质量提升
- **强化测试**: 增加集成测试和压力测试覆盖
- **代码审查**: 建立严格的代码审查机制
- **灰度发布**: 完善灰度发布和快速回滚能力
- **监控埋点**: 增加关键业务指标监控

### 5. 应急响应优化
- **标准化流程**: 建立标准化故障处理流程
- **自动化工具**: 开发自动化故障诊断和处理工具
- **知识沉淀**: 建立故障案例库和解决方案库
- **团队培训**: 定期进行故障处理培训和演练

## 📋 总结

通过对137个稳定性事件的分析，发现微服务架构下的稳定性挑战主要集中在：

1. **治理能力不足**: 大部分微服务问题缺乏有效的自动化治理手段
2. **第三方依赖风险**: 外部服务故障频繁影响业务稳定性
3. **基础设施脆弱**: 云资源故障和硬件问题影响较大
4. **应急响应依赖人工**: 缺乏自动化的故障检测和恢复机制

建议重点投入微服务治理平台建设，提升系统的自愈能力和故障隔离能力，减少人工干预，提高整体稳定性。
"""
    
    # 保存报告
    with open('microservice_analysis_report.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"分析完成！共分析了{total_cases}个案例")
    print(f"微服务相关事件: {len(microservice_cases)}条")
    print(f"使用治理能力事件: {len(governance_used_cases)}条")
    print("详细报告已保存到 microservice_analysis_report.md")

if __name__ == "__main__":
    main()
